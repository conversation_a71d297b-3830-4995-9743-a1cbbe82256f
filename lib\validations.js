import { z } from "zod"

export const personalInfoSchema = z.object({
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  phone: z.string().regex(/^\+?[\d\s-()]{10,}$/, "Please enter a valid phone number"),
  dateOfBirth: z.string().min(1, "Date of birth is required"),
  address: z.string().min(10, "Please provide a complete address"),
})

export const academicsSchema = z.object({
  collegeName: z.string().min(2, "College name is required"),
  degree: z.string().min(2, "Degree is required"),
  branch: z.string().min(2, "Branch/Major is required"),
  currentYear: z.string().min(1, "Current year is required"),
  cgpa: z.string().regex(/^\d+\.?\d*$/, "Please enter a valid CGPA"),
  backlogs: z.string().regex(/^\d+$/, "Please enter number of backlogs"),
  graduationYear: z.string().regex(/^\d{4}$/, "Please enter a valid year"),
})

export const projectsSchema = z.object({
  project1Title: z.string().min(2, "Project title is required"),
  project1Description: z.string().min(20, "Please provide a detailed description (min 20 characters)"),
  project1Technologies: z.string().min(2, "Technologies used are required"),
  project1Link: z.string().url("Please enter a valid URL").optional().or(z.literal("")),
  project2Title: z.string().optional(),
  project2Description: z.string().optional(),
  project2Technologies: z.string().optional(),
  project2Link: z.string().url("Please enter a valid URL").optional().or(z.literal("")),
})

export const domainSchema = z.object({
  preferredDomain: z.string().min(1, "Please select a preferred domain"),
  experience: z.string().min(1, "Please select your experience level"),
  motivation: z.string().min(50, "Please provide detailed motivation (min 50 characters)"),
  availability: z.string().min(1, "Please select your availability"),
})

export const fullFormSchema = personalInfoSchema.merge(academicsSchema).merge(projectsSchema).merge(domainSchema)
