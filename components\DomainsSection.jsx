"use client"
import { motion } from "framer-motion"
import { useHomeStaticData } from "@/context/HomeStaticDataContext"
import { GlassCard } from "@/components/ui/glass-card"
import { Badge } from "@/components/ui/badge"

export default function DomainsSection() {
  const { domains } = useHomeStaticData()

  return (
    <section className="py-20 relative">
      <div className="absolute inset-0 bg-gradient-to-b from-black to-gray-900" />

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">Internship Domains</h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Choose from our specialized internship programs designed to match industry demands
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {domains.map((domain, index) => (
            <motion.div
              key={domain.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
            >
              <GlassCard className="h-full hover:bg-white/20 transition-all duration-300 group cursor-pointer">
                <div
                  className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${domain.color} flex items-center justify-center text-2xl mb-6 group-hover:scale-110 transition-transform duration-300`}
                >
                  {domain.icon}
                </div>

                <h3 className="text-2xl font-bold text-white mb-4">{domain.title}</h3>

                <p className="text-gray-300 mb-6 leading-relaxed">{domain.description}</p>

                <div className="flex flex-wrap gap-2">
                  {domain.technologies.map((tech, techIndex) => (
                    <Badge
                      key={techIndex}
                      variant="secondary"
                      className="bg-white/10 text-white border-white/20 hover:bg-white/20"
                    >
                      {tech}
                    </Badge>
                  ))}
                </div>
              </GlassCard>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
