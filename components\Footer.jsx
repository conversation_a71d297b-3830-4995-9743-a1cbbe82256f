"use client"
import { motion } from "framer-motion"
import { useHomeStaticData } from "@/context/HomeStaticDataContext"
import { GlassCard } from "@/components/ui/glass-card"
import { Mail, Phone, MapPin, Linkedin, Twitter, Github } from "lucide-react"

export default function Footer() {
  const { footer } = useHomeStaticData()

  const iconMap = {
    linkedin: Linkedin,
    twitter: Twitter,
    github: Github,
  }

  return (
    <footer className="py-20 relative">
      <div className="absolute inset-0 bg-gradient-to-t from-black to-gray-900" />

      <div className="container mx-auto px-4 relative z-10">
        <motion.div initial={{ opacity: 0, y: 30 }} whileInView={{ opacity: 1, y: 0 }} transition={{ duration: 0.8 }}>
          <GlassCard>
            <div className="grid md:grid-cols-3 gap-8">
              {/* Company Info */}
              <div>
                <h3 className="text-2xl font-bold text-white mb-4">{footer.company}</h3>
                <p className="text-gray-300 mb-6 leading-relaxed">{footer.description}</p>
                <div className="flex space-x-4">
                  {footer.social.map((social, index) => {
                    const IconComponent = iconMap[social.icon]
                    return (
                      <a
                        key={index}
                        href={social.url}
                        className="w-10 h-10 bg-white/10 rounded-full flex items-center justify-center text-white hover:bg-white/20 transition-colors duration-300"
                      >
                        <IconComponent className="w-5 h-5" />
                      </a>
                    )
                  })}
                </div>
              </div>

              {/* Contact Info */}
              <div>
                <h4 className="text-xl font-bold text-white mb-4">Contact Us</h4>
                <div className="space-y-3">
                  <div className="flex items-center text-gray-300">
                    <Mail className="w-5 h-5 mr-3" />
                    <span>{footer.contact.email}</span>
                  </div>
                  <div className="flex items-center text-gray-300">
                    <Phone className="w-5 h-5 mr-3" />
                    <span>{footer.contact.phone}</span>
                  </div>
                  <div className="flex items-start text-gray-300">
                    <MapPin className="w-5 h-5 mr-3 mt-1" />
                    <span>{footer.contact.address}</span>
                  </div>
                </div>
              </div>

              {/* Quick Links */}
              <div>
                <h4 className="text-xl font-bold text-white mb-4">Quick Links</h4>
                <div className="space-y-2">
                  <a href="#hero" className="block text-gray-300 hover:text-white transition-colors duration-300">
                    Home
                  </a>
                  <a href="#domains" className="block text-gray-300 hover:text-white transition-colors duration-300">
                    Domains
                  </a>
                  <a
                    href="#internship-form"
                    className="block text-gray-300 hover:text-white transition-colors duration-300"
                  >
                    Apply Now
                  </a>
                  <a href="#" className="block text-gray-300 hover:text-white transition-colors duration-300">
                    Privacy Policy
                  </a>
                  <a href="#" className="block text-gray-300 hover:text-white transition-colors duration-300">
                    Terms of Service
                  </a>
                </div>
              </div>
            </div>

            <div className="border-t border-white/20 mt-8 pt-8 text-center">
              <p className="text-gray-400">© 2024 {footer.company}. All rights reserved.</p>
            </div>
          </GlassCard>
        </motion.div>
      </div>
    </footer>
  )
}
