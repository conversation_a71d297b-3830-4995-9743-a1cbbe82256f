"use client"
import { motion } from "framer-motion"
import { useHomeStaticData } from "@/context/HomeStaticDataContext"
import { GlassCard } from "@/components/ui/glass-card"

export default function MainSection() {
  const { mainSection } = useHomeStaticData()

  return (
    <section className="py-20 relative">
      <div className="absolute inset-0 bg-gradient-to-b from-gray-900 to-black" />

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">{mainSection.title}</h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">{mainSection.description}</p>
        </motion.div>

        <div className="grid md:grid-cols-3 gap-8">
          {mainSection.features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
            >
              <GlassCard className="text-center h-full hover:bg-white/20 transition-all duration-300">
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-2xl font-bold text-white mb-4">{feature.title}</h3>
                <p className="text-gray-300 leading-relaxed">{feature.description}</p>
              </GlassCard>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
