// Static constants for the application
export const HERO_DATA = {
  title: "Launch Your Tech Career with InstaIntern",
  subtitle: "Premium Internship Program",
  description:
    "Join our exclusive internship program and get hands-on experience with real-world projects, mentorship from industry experts, and guaranteed placement opportunities.",
  ctaText: "Apply Now",
  backgroundImage: "/placeholder.svg?height=800&width=1200",
}

export const MAIN_SECTION = {
  title: "Why Choose InstaIntern?",
  description:
    "InstaIntern aims to empower students by providing practical exposure through structured internship experiences that align with industry practices. Note: We do not guarantee mentorship, certification, or placement. All experiences are subject to availability and performance.",
  features: [
    {
      icon: "🚀",
      title: "Project Exposure",
      description: "Work on simulated industry-level projects to build real-world problem-solving skills",
    },
    {
      icon: "👨‍🏫",
      title: "Expert-Led Guidance",
      description: "Gain insights from seasoned industry experts through curated learning paths, pre-recorded sessions, and community support (subject to availability)",
    },
    {
      icon: "📈",
      title: "Career Development",
      description: "Build a stronger resume and portfolio through structured project work and optional placement assistance (not guaranteed)",
    },
  ],
}

export const DOMAINS = [
  {
    id: "web-dev",
    title: "Web Development",
    description: "Full-stack web development with modern frameworks",
    technologies: ["React", "Next.js", "Node.js", "MongoDB"],
    icon: "💻",
    color: "from-blue-500 to-purple-600",
  },
  {
    id: "mobile-dev",
    title: "Mobile Development",
    description: "Native and cross-platform mobile app development",
    technologies: ["React Native", "Flutter", "iOS", "Android"],
    icon: "📱",
    color: "from-green-500 to-blue-500",
  },
  {
    id: "data-science",
    title: "Data Science",
    description: "Machine learning, AI, and data analytics",
    technologies: ["Python", "TensorFlow", "Pandas", "Jupyter"],
    icon: "📊",
    color: "from-purple-500 to-pink-500",
  },
  {
    id: "gen-ai",
    title: "Generative AI",
    description: "Create AI-powered content, models, and media using deep learning",
    technologies: ["OpenAI", "Hugging Face", "Diffusers", "LangChain"],
    icon: "🧠",
    color: "from-indigo-600 to-cyan-500",
  },
  {
    id: "agentic-ai",
    title: "Agentic AI",
    description: "Build autonomous AI agents that reason, plan, and execute tasks",
    technologies: ["AutoGen", "CrewAI", "LangGraph", "GPT Agents"],
    icon: "🤖",
    color: "from-gray-700 to-violet-600",
  },
  {
    id: "python",
    title: "Python Development",
    description: "Backend development and automation with Python",
    technologies: ["Django", "Flask", "FastAPI", "PostgreSQL"],
    icon: "🐍",
    color: "from-yellow-500 to-red-500",
  },
  {
    id: "java",
    title: "Java Development",
    description: "Enterprise applications and microservices",
    technologies: ["Spring Boot", "Hibernate", "Maven", "Docker"],
    icon: "☕",
    color: "from-red-500 to-orange-500",
  },
];

export const WHY_US_DATA = {
  title: "What Makes Us Different",
  highlights: [
    {
      icon: "📜",
      title: "Letter of Recommendation",
      description: "LORs may be provided to eligible candidates based on performance and internal assessment. Subject to availability and discretion.",
    },
    {
      icon: "🏆",
      title: "Internship Certificate",
      description: "Receive an internship completion certificate upon meeting all evaluation criteria and project requirements.",
    },
    {
      icon: "💼",
      title: "Career Opportunities",
      description: "Exceptional performers may be referred to hiring partners. Please note: job placement is not guaranteed.",
    },
    {
      icon: "🎯",
      title: "Expert Guidance",
      description: "Access guidance and learning resources from experienced professionals. One-on-one mentorship may be offered, but is not guaranteed.",
    },
  ],
};


export const FOOTER_DATA = {
  company: "InstaIntern",
  description: "Empowering the next generation of tech professionals through premium internship programs.",
  contact: {
    email: "<EMAIL>",
    phone: "+****************",
    address: "123 Tech Street, Silicon Valley, CA 94000",
  },
  social: [
    { name: "LinkedIn", url: "#", icon: "linkedin" },
    { name: "Twitter", url: "#", icon: "twitter" },
    { name: "GitHub", url: "#", icon: "github" },
  ],
}

export const FORM_STEPS = [
  { id: 1, title: "Personal Info", description: "Basic information about you" },
  { id: 2, title: "Academics", description: "Your educational background" },
  { id: 3, title: "Projects", description: "Projects during academics" },
  { id: 4, title: "Domain & Submit", description: "Choose domain and submit" },
]
