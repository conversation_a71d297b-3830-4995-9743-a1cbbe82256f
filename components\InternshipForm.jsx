"use client"
import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { useHomeStaticData } from "@/context/HomeStaticDataContext"
import { GlassCard } from "@/components/ui/glass-card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { personalInfoSchema, academicsSchema, projectsSchema, domainSchema } from "@/lib/validations"
import { ChevronLeft, ChevronRight, CheckCircle } from "lucide-react"

export default function InternshipForm() {
  const { formSteps, domains } = useHomeStaticData()
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState({})
  const [errors, setErrors] = useState({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const updateFormData = (stepData) => {
    setFormData((prev) => ({ ...prev, ...stepData }))
  }

  const validateStep = (step, data) => {
    try {
      switch (step) {
        case 1:
          personalInfoSchema.parse(data)
          break
        case 2:
          academicsSchema.parse(data)
          break
        case 3:
          projectsSchema.parse(data)
          break
        case 4:
          domainSchema.parse(data)
          break
      }
      return {}
    } catch (error) {
      const fieldErrors = {}
      error.errors.forEach((err) => {
        fieldErrors[err.path[0]] = err.message
      })
      return fieldErrors
    }
  }

  const handleNext = () => {
    const stepErrors = validateStep(currentStep, formData)
    setErrors(stepErrors)

    if (Object.keys(stepErrors).length === 0) {
      setCurrentStep((prev) => Math.min(prev + 1, 4))
    }
  }

  const handlePrevious = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 1))
  }

  const handleSubmit = async () => {
    const stepErrors = validateStep(4, formData)
    setErrors(stepErrors)

    if (Object.keys(stepErrors).length === 0) {
      setIsSubmitting(true)
      try {
        const response = await fetch("/api/applyInternship", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(formData),
        })

        if (response.ok) {
          setIsSubmitted(true)
        } else {
          throw new Error("Submission failed")
        }
      } catch (error) {
        console.error("Submission error:", error)
        alert("There was an error submitting your application. Please try again.")
      } finally {
        setIsSubmitting(false)
      }
    }
  }

  if (isSubmitted) {
    return (
      <section id="internship-form" className="py-20 relative">
        <div className="absolute inset-0 bg-gradient-to-b from-purple-900 to-blue-900" />
        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className="max-w-md mx-auto text-center"
          >
            <GlassCard>
              <CheckCircle className="w-16 h-16 text-green-400 mx-auto mb-4" />
              <h3 className="text-2xl font-bold text-white mb-4">Application Submitted!</h3>
              <p className="text-gray-300">Thank you for applying! Our HR department will contact you soon.</p>
            </GlassCard>
          </motion.div>
        </div>
      </section>
    )
  }

  return (
    <section id="internship-form" className="py-20 relative">
      <div className="absolute inset-0 bg-gradient-to-b from-purple-900 to-blue-900" />

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">Apply for Internship</h2>
          <p className="text-xl text-gray-300">Complete the form below to start your journey with us</p>
        </motion.div>

        <div className="max-w-4xl mx-auto">
          {/* Progress Steps */}
          <div className="flex justify-between mb-8">
            {formSteps.map((step) => (
              <div key={step.id} className={`flex items-center ${step.id < formSteps.length ? "flex-1" : ""}`}>
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold ${
                    currentStep >= step.id ? "bg-blue-500 text-white" : "bg-white/20 text-gray-400"
                  }`}
                >
                  {step.id}
                </div>
                <div className="ml-3 hidden md:block">
                  <p className={`text-sm font-medium ${currentStep >= step.id ? "text-white" : "text-gray-400"}`}>
                    {step.title}
                  </p>
                  <p className="text-xs text-gray-400">{step.description}</p>
                </div>
                {step.id < formSteps.length && (
                  <div className={`flex-1 h-0.5 mx-4 ${currentStep > step.id ? "bg-blue-500" : "bg-white/20"}`} />
                )}
              </div>
            ))}
          </div>

          <GlassCard>
            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                {currentStep === 1 && (
                  <PersonalInfoStep formData={formData} updateFormData={updateFormData} errors={errors} />
                )}
                {currentStep === 2 && (
                  <AcademicsStep formData={formData} updateFormData={updateFormData} errors={errors} />
                )}
                {currentStep === 3 && (
                  <ProjectsStep formData={formData} updateFormData={updateFormData} errors={errors} />
                )}
                {currentStep === 4 && (
                  <DomainStep formData={formData} updateFormData={updateFormData} errors={errors} domains={domains} />
                )}
              </motion.div>
            </AnimatePresence>

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-8">
              <Button
                onClick={handlePrevious}
                disabled={currentStep === 1}
                variant="outline"
                className="bg-white/10 border-white/20 text-white hover:bg-white/20"
              >
                <ChevronLeft className="w-4 h-4 mr-2" />
                Previous
              </Button>

              {currentStep < 4 ? (
                <Button onClick={handleNext} className="bg-blue-500 hover:bg-blue-600 text-white">
                  Next
                  <ChevronRight className="w-4 h-4 ml-2" />
                </Button>
              ) : (
                <Button
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="bg-green-500 hover:bg-green-600 text-white"
                >
                  {isSubmitting ? "Submitting..." : "Submit Application"}
                </Button>
              )}
            </div>
          </GlassCard>
        </div>
      </div>
    </section>
  )
}

// Step Components
function PersonalInfoStep({ formData, updateFormData, errors }) {
  const handleChange = (field, value) => {
    updateFormData({ [field]: value })
  }

  return (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold text-white mb-6">Personal Information</h3>

      <div className="grid md:grid-cols-2 gap-6">
        <div>
          <Label htmlFor="firstName" className="text-white">
            First Name
          </Label>
          <Input
            id="firstName"
            value={formData.firstName || ""}
            onChange={(e) => handleChange("firstName", e.target.value)}
            className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
            placeholder="Enter your first name"
          />
          {errors.firstName && <p className="text-red-400 text-sm mt-1">{errors.firstName}</p>}
        </div>

        <div>
          <Label htmlFor="lastName" className="text-white">
            Last Name
          </Label>
          <Input
            id="lastName"
            value={formData.lastName || ""}
            onChange={(e) => handleChange("lastName", e.target.value)}
            className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
            placeholder="Enter your last name"
          />
          {errors.lastName && <p className="text-red-400 text-sm mt-1">{errors.lastName}</p>}
        </div>
      </div>

      <div>
        <Label htmlFor="email" className="text-white">
          Email Address
        </Label>
        <Input
          id="email"
          type="email"
          value={formData.email || ""}
          onChange={(e) => handleChange("email", e.target.value)}
          className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
          placeholder="Enter your email address"
        />
        {errors.email && <p className="text-red-400 text-sm mt-1">{errors.email}</p>}
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        <div>
          <Label htmlFor="phone" className="text-white">
            Phone Number
          </Label>
          <Input
            id="phone"
            value={formData.phone || ""}
            onChange={(e) => handleChange("phone", e.target.value)}
            className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
            placeholder="Enter your phone number"
          />
          {errors.phone && <p className="text-red-400 text-sm mt-1">{errors.phone}</p>}
        </div>

        <div>
          <Label htmlFor="dateOfBirth" className="text-white">
            Date of Birth
          </Label>
          <Input
            id="dateOfBirth"
            type="date"
            value={formData.dateOfBirth || ""}
            onChange={(e) => handleChange("dateOfBirth", e.target.value)}
            className="bg-white/10 border-white/20 text-white"
          />
          {errors.dateOfBirth && <p className="text-red-400 text-sm mt-1">{errors.dateOfBirth}</p>}
        </div>
      </div>

      <div>
        <Label htmlFor="address" className="text-white">
          Address
        </Label>
        <Textarea
          id="address"
          value={formData.address || ""}
          onChange={(e) => handleChange("address", e.target.value)}
          className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
          placeholder="Enter your complete address"
          rows={3}
        />
        {errors.address && <p className="text-red-400 text-sm mt-1">{errors.address}</p>}
      </div>
    </div>
  )
}

function AcademicsStep({ formData, updateFormData, errors }) {
  const handleChange = (field, value) => {
    updateFormData({ [field]: value })
  }

  return (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold text-white mb-6">Academic Information</h3>

      <div>
        <Label htmlFor="collegeName" className="text-white">
          College/University Name
        </Label>
        <Input
          id="collegeName"
          value={formData.collegeName || ""}
          onChange={(e) => handleChange("collegeName", e.target.value)}
          className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
          placeholder="Enter your college name"
        />
        {errors.collegeName && <p className="text-red-400 text-sm mt-1">{errors.collegeName}</p>}
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        <div>
          <Label htmlFor="degree" className="text-white">
            Degree
          </Label>
          <Select onValueChange={(value) => handleChange("degree", value)}>
            <SelectTrigger className="bg-white/10 border-white/20 text-white">
              <SelectValue placeholder="Select your degree" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="btech">B.Tech</SelectItem>
              <SelectItem value="be">B.E</SelectItem>
              <SelectItem value="bca">BCA</SelectItem>
              <SelectItem value="bsc">B.Sc</SelectItem>
              <SelectItem value="mtech">M.Tech</SelectItem>
              <SelectItem value="mca">MCA</SelectItem>
              <SelectItem value="msc">M.Sc</SelectItem>
            </SelectContent>
          </Select>
          {errors.degree && <p className="text-red-400 text-sm mt-1">{errors.degree}</p>}
        </div>

        <div>
          <Label htmlFor="branch" className="text-white">
            Branch/Major
          </Label>
          <Input
            id="branch"
            value={formData.branch || ""}
            onChange={(e) => handleChange("branch", e.target.value)}
            className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
            placeholder="e.g., Computer Science"
          />
          {errors.branch && <p className="text-red-400 text-sm mt-1">{errors.branch}</p>}
        </div>
      </div>

      <div className="grid md:grid-cols-3 gap-6">
        <div>
          <Label htmlFor="currentYear" className="text-white">
            Current Year
          </Label>
          <Select onValueChange={(value) => handleChange("currentYear", value)}>
            <SelectTrigger className="bg-white/10 border-white/20 text-white">
              <SelectValue placeholder="Select year" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">1st Year</SelectItem>
              <SelectItem value="2">2nd Year</SelectItem>
              <SelectItem value="3">3rd Year</SelectItem>
              <SelectItem value="4">4th Year</SelectItem>
            </SelectContent>
          </Select>
          {errors.currentYear && <p className="text-red-400 text-sm mt-1">{errors.currentYear}</p>}
        </div>

        <div>
          <Label htmlFor="cgpa" className="text-white">
            CGPA/Percentage
          </Label>
          <Input
            id="cgpa"
            value={formData.cgpa || ""}
            onChange={(e) => handleChange("cgpa", e.target.value)}
            className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
            placeholder="e.g., 8.5 or 85%"
          />
          {errors.cgpa && <p className="text-red-400 text-sm mt-1">{errors.cgpa}</p>}
        </div>

        <div>
          <Label htmlFor="backlogs" className="text-white">
            Number of Backlogs
          </Label>
          <Input
            id="backlogs"
            value={formData.backlogs || ""}
            onChange={(e) => handleChange("backlogs", e.target.value)}
            className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
            placeholder="0"
          />
          {errors.backlogs && <p className="text-red-400 text-sm mt-1">{errors.backlogs}</p>}
        </div>
      </div>

      <div>
        <Label htmlFor="graduationYear" className="text-white">
          Expected Graduation Year
        </Label>
        <Input
          id="graduationYear"
          value={formData.graduationYear || ""}
          onChange={(e) => handleChange("graduationYear", e.target.value)}
          className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
          placeholder="e.g., 2025"
        />
        {errors.graduationYear && <p className="text-red-400 text-sm mt-1">{errors.graduationYear}</p>}
      </div>
    </div>
  )
}

function ProjectsStep({ formData, updateFormData, errors }) {
  const handleChange = (field, value) => {
    updateFormData({ [field]: value })
  }

  return (
    <div className="space-y-8">
      <h3 className="text-2xl font-bold text-white mb-6">Projects During Academics</h3>

      {/* Project 1 - Required */}
      <div className="space-y-4">
        <h4 className="text-xl font-semibold text-white">Project 1 (Required)</h4>

        <div>
          <Label htmlFor="project1Title" className="text-white">
            Project Title
          </Label>
          <Input
            id="project1Title"
            value={formData.project1Title || ""}
            onChange={(e) => handleChange("project1Title", e.target.value)}
            className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
            placeholder="Enter project title"
          />
          {errors.project1Title && <p className="text-red-400 text-sm mt-1">{errors.project1Title}</p>}
        </div>

        <div>
          <Label htmlFor="project1Description" className="text-white">
            Project Description
          </Label>
          <Textarea
            id="project1Description"
            value={formData.project1Description || ""}
            onChange={(e) => handleChange("project1Description", e.target.value)}
            className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
            placeholder="Describe your project in detail"
            rows={4}
          />
          {errors.project1Description && <p className="text-red-400 text-sm mt-1">{errors.project1Description}</p>}
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="project1Technologies" className="text-white">
              Technologies Used
            </Label>
            <Input
              id="project1Technologies"
              value={formData.project1Technologies || ""}
              onChange={(e) => handleChange("project1Technologies", e.target.value)}
              className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
              placeholder="e.g., React, Node.js, MongoDB"
            />
            {errors.project1Technologies && <p className="text-red-400 text-sm mt-1">{errors.project1Technologies}</p>}
          </div>

          <div>
            <Label htmlFor="project1Link" className="text-white">
              Project Link (Optional)
            </Label>
            <Input
              id="project1Link"
              value={formData.project1Link || ""}
              onChange={(e) => handleChange("project1Link", e.target.value)}
              className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
              placeholder="https://github.com/username/project"
            />
            {errors.project1Link && <p className="text-red-400 text-sm mt-1">{errors.project1Link}</p>}
          </div>
        </div>
      </div>

      {/* Project 2 - Optional */}
      <div className="space-y-4">
        <h4 className="text-xl font-semibold text-white">Project 2 (Optional)</h4>

        <div>
          <Label htmlFor="project2Title" className="text-white">
            Project Title
          </Label>
          <Input
            id="project2Title"
            value={formData.project2Title || ""}
            onChange={(e) => handleChange("project2Title", e.target.value)}
            className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
            placeholder="Enter project title"
          />
        </div>

        <div>
          <Label htmlFor="project2Description" className="text-white">
            Project Description
          </Label>
          <Textarea
            id="project2Description"
            value={formData.project2Description || ""}
            onChange={(e) => handleChange("project2Description", e.target.value)}
            className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
            placeholder="Describe your project in detail"
            rows={4}
          />
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="project2Technologies" className="text-white">
              Technologies Used
            </Label>
            <Input
              id="project2Technologies"
              value={formData.project2Technologies || ""}
              onChange={(e) => handleChange("project2Technologies", e.target.value)}
              className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
              placeholder="e.g., Python, Django, PostgreSQL"
            />
          </div>

          <div>
            <Label htmlFor="project2Link" className="text-white">
              Project Link (Optional)
            </Label>
            <Input
              id="project2Link"
              value={formData.project2Link || ""}
              onChange={(e) => handleChange("project2Link", e.target.value)}
              className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
              placeholder="https://github.com/username/project"
            />
          </div>
        </div>
      </div>
    </div>
  )
}

function DomainStep({ formData, updateFormData, errors, domains }) {
  const handleChange = (field, value) => {
    updateFormData({ [field]: value })
  }

  return (
    <div className="space-y-6">
      <h3 className="text-2xl font-bold text-white mb-6">Domain Selection & Final Details</h3>

      <div>
        <Label htmlFor="preferredDomain" className="text-white">
          Preferred Internship Domain
        </Label>
        <Select onValueChange={(value) => handleChange("preferredDomain", value)}>
          <SelectTrigger className="bg-white/10 border-white/20 text-white">
            <SelectValue placeholder="Select your preferred domain" />
          </SelectTrigger>
          <SelectContent>
            {domains.map((domain) => (
              <SelectItem key={domain.id} value={domain.id}>
                {domain.title}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {errors.preferredDomain && <p className="text-red-400 text-sm mt-1">{errors.preferredDomain}</p>}
      </div>

      <div>
        <Label htmlFor="experience" className="text-white">
          Experience Level
        </Label>
        <Select onValueChange={(value) => handleChange("experience", value)}>
          <SelectTrigger className="bg-white/10 border-white/20 text-white">
            <SelectValue placeholder="Select your experience level" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="beginner">Beginner (0-6 months)</SelectItem>
            <SelectItem value="intermediate">Intermediate (6 months - 2 years)</SelectItem>
            <SelectItem value="advanced">Advanced (2+ years)</SelectItem>
          </SelectContent>
        </Select>
        {errors.experience && <p className="text-red-400 text-sm mt-1">{errors.experience}</p>}
      </div>

      <div>
        <Label htmlFor="availability" className="text-white">
          Availability
        </Label>
        <Select onValueChange={(value) => handleChange("availability", value)}>
          <SelectTrigger className="bg-white/10 border-white/20 text-white">
            <SelectValue placeholder="Select your availability" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="full-time">Full-time (40+ hours/week)</SelectItem>
            <SelectItem value="part-time">Part-time (20-30 hours/week)</SelectItem>
            <SelectItem value="flexible">Flexible (10-20 hours/week)</SelectItem>
          </SelectContent>
        </Select>
        {errors.availability && <p className="text-red-400 text-sm mt-1">{errors.availability}</p>}
      </div>

      <div>
        <Label htmlFor="motivation" className="text-white">
          Why do you want to join this internship?
        </Label>
        <Textarea
          id="motivation"
          value={formData.motivation || ""}
          onChange={(e) => handleChange("motivation", e.target.value)}
          className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
          placeholder="Tell us about your motivation, career goals, and what you hope to achieve through this internship..."
          rows={5}
        />
        {errors.motivation && <p className="text-red-400 text-sm mt-1">{errors.motivation}</p>}
      </div>
    </div>
  )
}
