"use client"
import { motion } from "framer-motion"
import { cn } from "@/lib/utils"

export const GlassCard = ({ children, className, ...props }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className={cn("backdrop-blur-lg bg-white/10 border border-white/20 rounded-2xl p-6 shadow-xl", className)}
      {...props}
    >
      {children}
    </motion.div>
  )
}
