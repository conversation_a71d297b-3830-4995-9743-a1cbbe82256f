"use client"
import { createContext, useContext } from "react"
import { HERO_DATA, MAIN_SECTION, DOMAINS, WHY_US_DATA, FOOTER_DATA, FORM_STEPS } from "@/lib/const"

const HomeStaticDataContext = createContext()

export const useHomeStaticData = () => {
  const context = useContext(HomeStaticDataContext)
  if (!context) {
    throw new Error("useHomeStaticData must be used within HomeStaticDataProvider")
  }
  return context
}

export const HomeStaticDataProvider = ({ children }) => {
  const contextValue = {
    hero: HERO_DATA,
    mainSection: MAIN_SECTION,
    domains: DOMAINS,
    whyUs: WHY_US_DATA,
    footer: FOOTER_DATA,
    formSteps: FORM_STEPS,
  }

  return <HomeStaticDataContext.Provider value={contextValue}>{children}</HomeStaticDataContext.Provider>
}
